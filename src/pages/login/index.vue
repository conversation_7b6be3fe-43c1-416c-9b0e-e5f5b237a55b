<route lang="jsonc" type="page">
{
  "layout": "default",
  "needLogin": false,
  "style": {
    "navigationBarTitleText": "登录",
    // "navigationStyle": "custom"
  }
}
</route>

<script lang="ts" setup>
import { useMessage } from 'wot-design-uni'
import { useUserStore } from '@/store/user'
import { toast } from '@/utils/toast'

const userStore = useUserStore()

const message = useMessage()

const appName = import.meta.env.VITE_APP_TITLE

console.log(appName)

// 表单数据
const loginForm = ref({
  username: '',
  password: '',
})

// 加载状态
const loading = ref(false)

function handleClear() {
  uni.setStorageSync('loginForm', loginForm.value)
}

// 登录处理
async function handleLogin() {
  uni.setStorageSync('loginForm', loginForm.value)
  // 表单验证
  if (!loginForm.value.username.trim()) {
    toast.error('请输入账号')
    return
  }

  if (!loginForm.value.password.trim()) {
    toast.error('请输入密码')
    return
  }

  loading.value = true

  try {
    // 构造登录参数，不需要验证码时传空值
    const loginData = {
      username: loginForm.value.username,
      password: loginForm.value.password,
      code: '',
      uuid: '',
    }

    const res = await userStore.login(loginData)
    if (res.code === 200) {
      uni.reLaunch({
        url: '/pages/index/index',
      })
    }
    else {
      message.alert({
        title: '登录失败',
        msg: res.msg,
      })
    }
  }
  catch (error) {
    message.alert({
      title: '登录失败',
      msg: '登录失败，请检查账号密码',
    })
  }
  finally {
    loading.value = false
  }
}

onMounted(() => {
  const savedForm = uni.getStorageSync('loginForm')
  console.log(savedForm)
  if (savedForm) {
    loginForm.value = savedForm
  }
})
</script>

<template>
  <view class="bg-[url('/static/images/bg.png')]" h-screen flex items-center flex-justify-center overflow-hidden bg-contain>
    <wd-card
      custom-class="w-327px border-rd-15px !bg-[#F2F9FA]"
      custom-content-class="text-black"
    >
      <!-- header -->
      <view align-center border-b-1-px flex items-center gap-12px border-b="#004166" border-b-solid py-15px>
        <img src="/static/logo.svg" width="38" height="38">
        <text flex-1 text-42rpx color="#004166">
          走线架AI
        </text>
        <view flex items-end gap-5px>
          <svg xmlns="http://www.w3.org/2000/svg" width="6" height="6" viewBox="0 0 6 6" fill="none">
            <circle cx="3" cy="3" r="2.5" fill="#004166" stroke="#004166" />
          </svg>
          <svg xmlns="http://www.w3.org/2000/svg" width="6" height="6" viewBox="0 0 6 6" fill="none">
            <circle cx="3" cy="3" r="2.75" stroke="#004166" stroke-width="0.5" />
          </svg>
          <svg xmlns="http://www.w3.org/2000/svg" width="6" height="6" viewBox="0 0 6 6" fill="none">
            <circle cx="3" cy="3" r="3" fill="#90C31F" />
          </svg>
        </view>
      </view>

      <!-- center -->
      <view mb-12px mt-16px text-46rpx>
        <text color="#004166">
          {{ appName }}
        </text>
      </view>

      <view>
        <view mt-12px>
          <text color="#004166">
            登录账号
          </text>
        </view>
        <wd-input
          v-model="loginForm.username"
          placeholder="请输入账号"
          clearable
          :disabled="loading"
          !bg-transparent
          @clear="handleClear"
        />
      </view>

      <view mt-12px>
        <text color="#004166">
          登录密码
        </text>
      </view>
      <wd-input
        v-model="loginForm.password"
        placeholder="请输入登录密码"
        :show-password="true"
        clearable
        :disabled="loading"
        !bg-transparent
        @clear="handleClear"
      />

      <img src="" my-5px text-align-center width="300px" height="238px">

      <!-- footer -->
      <view mb-16px>
        <wd-button
          block
          type="primary"
          :loading="loading"
          :disabled="loading"
          @click="handleLogin"
        >
          {{ loading ? '登录中...' : '登录' }}
        </wd-button>
      </view>
    </wd-card>
  </view>
</template>

<style lang="scss" scoped>
// 无需额外样式，使用内联样式
:dee
</style>
