<route lang="jsonc" type="page">
{
  "layout": "tabbar",
  "style": {
    "navigationBarTitleText": "个人中心"
  }
}
</route>

<script lang="ts" setup>
import { ref } from 'vue'

const model = ref({
  value1: '',
  platform: '',
})

const platformList = ref([
  {
    label: '润建',
    value: '润建',
  },
  {
    label: '润建2',
    value: '润建2',
  },
])

function handleLogout() {
}
</script>

<template>
  <view flex flex-col items-center>
    <view my-10rpx h-180rpx w-714rpx flex flex-items-center border-2rpx border-cyan-600 rounded-2xl border-solid bg-white shadow-lg>
      <view>
        <image src="/static/logo.svg" mx-20rpx h-120rpx w-120rpx />
      </view>
      <view>
        <view mb-6px>
          姓名: 张三
        </view>
        <view>手机号: 13800000000</view>
      </view>
    </view>

    <view mt-20rpx w-674rpx border-2rpx border-cyan-600 rounded-lg border-solid bg-white p-20rpx shadow-lg>
      <text text-sm>部门：</text>
      <wd-input
        v-model="model.value1"
        prop="value1"
        clearable
        placeholder="请选择"
        :rules="[{ required: true, message: '请选择部门' }]"
        class="custom-border"
      />
      <text text-sm>所属公司：</text>
      <wd-select-picker
        v-model="model.platform"
        prop="platform"
        :columns="platformList"
        placeholder="请选择"
        :rules="[{ required: true, message: '请选择所属公司' }]"
        class="custom-border"
      />
      <text text-sm>岗位角色：</text>
      <wd-input
        v-model="model.value1"
        label-width="100px"
        prop="value1"
        clearable
        placeholder="请选择"
        :rules="[{ required: true, message: '请选择岗位角色' }]"
        class="custom-border"
      />
    </view>

    <view class="logout-btn-wrapper" fixed bottom-120rpx z-10>
      <wd-button
        type="primary"
        block
        size="large"
        w-718rpx
        @click="handleLogout"
      >
        退出登录
      </wd-button>
    </view>
  </view>
</template>

<style lang="scss" scoped>
:deep(.wd-cell) {
  padding-left: 0;
}
.custom-border :deep(.wd-input__inner),
.custom-border :deep(.wd-cell__wrapper) {
  border: 1px solid #dcdfe6;
  border-radius: 8rpx;
  padding: 16rpx 24rpx;
}
.logout-btn-wrapper {
  :deep(.wd-button.is-round) {
    border-radius: 8rpx;
  }
}
</style>
